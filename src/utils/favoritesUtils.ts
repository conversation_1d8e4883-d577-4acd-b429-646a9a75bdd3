'use client';

import { favoritesStorage } from '@/lib/storage';

/**
 * Utilities for managing favorite cocktails using Supabase with localStorage fallback
 */

/**
 * Get all favorite cocktail IDs
 */
export async function getFavoriteIds(): Promise<string[]> {
  return await favoritesStorage.getFavorites();
}

/**
 * Get all favorite cocktail IDs (synchronous version for backward compatibility)
 * This will return cached data from localStorage
 */
export function getFavoriteIdsSync(): string[] {
  if (typeof window === 'undefined') return [];

  try {
    const favorites = localStorage.getItem('cocktailflow-favorites');
    return favorites ? JSON.parse(favorites) : [];
  } catch (error) {
    console.error('Error reading favorites from localStorage:', error);
    return [];
  }
}

/**
 * Check if a cocktail is favorited (synchronous version using cached data)
 */
export function isFavorite(cocktailId: string): boolean {
  const favorites = getFavoriteIdsSync();
  return favorites.includes(cocktailId);
}

/**
 * Check if a cocktail is favorited (async version)
 */
export async function isFavoriteAsync(cocktailId: string): Promise<boolean> {
  const favorites = await getFavoriteIds();
  return favorites.includes(cocktailId);
}

/**
 * Add a cocktail to favorites
 */
export async function addToFavorites(cocktailId: string): Promise<void> {
  await favoritesStorage.addFavorite(cocktailId);
}

/**
 * Remove a cocktail from favorites
 */
export async function removeFromFavorites(cocktailId: string): Promise<void> {
  await favoritesStorage.removeFavorite(cocktailId);
}

/**
 * Toggle favorite status of a cocktail
 */
export async function toggleFavorite(cocktailId: string): Promise<boolean> {
  const isCurrentlyFavorite = await isFavoriteAsync(cocktailId);

  if (isCurrentlyFavorite) {
    await removeFromFavorites(cocktailId);
    return false;
  } else {
    await addToFavorites(cocktailId);
    return true;
  }
}

/**
 * Toggle favorite status of a cocktail (synchronous version for backward compatibility)
 */
export function toggleFavoriteSync(cocktailId: string): boolean {
  const isCurrentlyFavorite = isFavorite(cocktailId);

  // Use async functions but don't wait for them
  if (isCurrentlyFavorite) {
    removeFromFavorites(cocktailId);
    return false;
  } else {
    addToFavorites(cocktailId);
    return true;
  }
}

/**
 * Clear all favorites
 */
export async function clearAllFavorites(): Promise<void> {
  try {
    const favorites = await getFavoriteIds();
    for (const cocktailId of favorites) {
      await removeFromFavorites(cocktailId);
    }
  } catch (error) {
    console.error('Error clearing all favorites:', error);
  }
}

/**
 * Get count of favorite cocktails
 */
export async function getFavoritesCount(): Promise<number> {
  try {
    const favorites = await getFavoriteIds();
    return favorites.length;
  } catch (error) {
    console.error('Error getting favorites count:', error);
    return 0;
  }
}

/**
 * Get count of favorite cocktails (synchronous version)
 */
export function getFavoritesCountSync(): number {
  return getFavoriteIdsSync().length;
}
