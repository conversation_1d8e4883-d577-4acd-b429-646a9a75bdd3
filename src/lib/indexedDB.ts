/**
 * IndexedDB storage layer for offline-first architecture
 * Provides better performance and larger storage capacity than localStorage
 */

import { openDB, DBSchema, IDBPDatabase } from 'idb';
import { Cocktail, Ingredient, GlassType } from '@/types/cocktail';
import { ShoppingListItem } from '@/utils/shoppingListUtils';

// Database schema definition
interface TippleDB extends DBSchema {
  cocktails: {
    key: string;
    value: Cocktail & { lastModified: number; syncStatus: 'synced' | 'pending' | 'conflict' };
  };
  ingredients: {
    key: string;
    value: Ingredient & { lastModified: number; syncStatus: 'synced' | 'pending' | 'conflict' };
  };
  glassTypes: {
    key: string;
    value: GlassType & { lastModified: number; syncStatus: 'synced' | 'pending' | 'conflict' };
  };
  favorites: {
    key: string;
    value: { 
      id: string; 
      cocktailId: string; 
      userId: string; 
      createdAt: number; 
      lastModified: number; 
      syncStatus: 'synced' | 'pending' | 'conflict';
    };
  };
  shoppingList: {
    key: string;
    value: ShoppingListItem & { 
      lastModified: number; 
      syncStatus: 'synced' | 'pending' | 'conflict';
    };
  };
  metadata: {
    key: string;
    value: {
      key: string;
      value: any;
      lastModified: number;
    };
  };
}

const DB_NAME = 'TippleDB';
const DB_VERSION = 1;

let dbInstance: IDBPDatabase<TippleDB> | null = null;

/**
 * Initialize and open the IndexedDB database
 */
export async function initDB(): Promise<IDBPDatabase<TippleDB>> {
  if (dbInstance) {
    return dbInstance;
  }

  try {
    dbInstance = await openDB<TippleDB>(DB_NAME, DB_VERSION, {
      upgrade(db, oldVersion, newVersion, transaction) {
        console.log(`Upgrading database from version ${oldVersion} to ${newVersion}`);

        // Create object stores
        if (!db.objectStoreNames.contains('cocktails')) {
          const cocktailStore = db.createObjectStore('cocktails', { keyPath: 'id' });
          cocktailStore.createIndex('category', 'category');
          cocktailStore.createIndex('difficulty', 'difficulty');
          cocktailStore.createIndex('syncStatus', 'syncStatus');
          cocktailStore.createIndex('lastModified', 'lastModified');
        }

        if (!db.objectStoreNames.contains('ingredients')) {
          const ingredientStore = db.createObjectStore('ingredients', { keyPath: 'id' });
          ingredientStore.createIndex('category', 'category');
          ingredientStore.createIndex('alcoholic', 'alcoholic');
          ingredientStore.createIndex('syncStatus', 'syncStatus');
          ingredientStore.createIndex('lastModified', 'lastModified');
        }

        if (!db.objectStoreNames.contains('glassTypes')) {
          const glassTypeStore = db.createObjectStore('glassTypes', { keyPath: 'id' });
          glassTypeStore.createIndex('syncStatus', 'syncStatus');
          glassTypeStore.createIndex('lastModified', 'lastModified');
        }

        if (!db.objectStoreNames.contains('favorites')) {
          const favoritesStore = db.createObjectStore('favorites', { keyPath: 'id' });
          favoritesStore.createIndex('cocktailId', 'cocktailId');
          favoritesStore.createIndex('userId', 'userId');
          favoritesStore.createIndex('syncStatus', 'syncStatus');
          favoritesStore.createIndex('lastModified', 'lastModified');
        }

        if (!db.objectStoreNames.contains('shoppingList')) {
          const shoppingListStore = db.createObjectStore('shoppingList', { keyPath: 'id' });
          shoppingListStore.createIndex('syncStatus', 'syncStatus');
          shoppingListStore.createIndex('lastModified', 'lastModified');
        }

        if (!db.objectStoreNames.contains('metadata')) {
          db.createObjectStore('metadata', { keyPath: 'key' });
        }
      },
      blocked() {
        console.warn('Database upgrade blocked. Please close other tabs.');
      },
      blocking() {
        console.warn('Database is blocking a newer version. Closing connection.');
        dbInstance?.close();
        dbInstance = null;
      },
      terminated() {
        console.error('Database connection was terminated unexpectedly.');
        dbInstance = null;
      }
    });

    console.log('IndexedDB initialized successfully');
    return dbInstance;
  } catch (error) {
    console.error('Failed to initialize IndexedDB:', error);
    throw error;
  }
}

/**
 * Generic storage operations
 */
export class IndexedDBStorage {
  private db: IDBPDatabase<TippleDB> | null = null;

  async init(): Promise<void> {
    this.db = await initDB();
  }

  private async ensureDB(): Promise<IDBPDatabase<TippleDB>> {
    if (!this.db) {
      await this.init();
    }
    return this.db!;
  }

  // Cocktails operations
  async getCocktails(): Promise<Cocktail[]> {
    const db = await this.ensureDB();
    const cocktails = await db.getAll('cocktails');
    return cocktails.map(c => {
      const { lastModified, syncStatus, ...cocktail } = c;
      return cocktail;
    });
  }

  async getCocktail(id: string): Promise<Cocktail | undefined> {
    const db = await this.ensureDB();
    const cocktail = await db.get('cocktails', id);
    if (!cocktail) return undefined;
    const { lastModified, syncStatus, ...result } = cocktail;
    return result;
  }

  async saveCocktail(cocktail: Cocktail, syncStatus: 'synced' | 'pending' = 'pending'): Promise<void> {
    const db = await this.ensureDB();
    await db.put('cocktails', {
      ...cocktail,
      lastModified: Date.now(),
      syncStatus
    });
  }

  async saveCocktails(cocktails: Cocktail[], syncStatus: 'synced' | 'pending' = 'synced'): Promise<void> {
    const db = await this.ensureDB();
    const tx = db.transaction('cocktails', 'readwrite');
    const now = Date.now();
    
    await Promise.all([
      ...cocktails.map(cocktail => 
        tx.store.put({
          ...cocktail,
          lastModified: now,
          syncStatus
        })
      ),
      tx.done
    ]);
  }

  async deleteCocktail(id: string): Promise<void> {
    const db = await this.ensureDB();
    await db.delete('cocktails', id);
  }

  // Ingredients operations
  async getIngredients(): Promise<Ingredient[]> {
    const db = await this.ensureDB();
    const ingredients = await db.getAll('ingredients');
    return ingredients.map(i => {
      const { lastModified, syncStatus, ...ingredient } = i;
      return ingredient;
    });
  }

  async getIngredient(id: string): Promise<Ingredient | undefined> {
    const db = await this.ensureDB();
    const ingredient = await db.get('ingredients', id);
    if (!ingredient) return undefined;
    const { lastModified, syncStatus, ...result } = ingredient;
    return result;
  }

  async saveIngredient(ingredient: Ingredient, syncStatus: 'synced' | 'pending' = 'pending'): Promise<void> {
    const db = await this.ensureDB();
    await db.put('ingredients', {
      ...ingredient,
      lastModified: Date.now(),
      syncStatus
    });
  }

  async saveIngredients(ingredients: Ingredient[], syncStatus: 'synced' | 'pending' = 'synced'): Promise<void> {
    const db = await this.ensureDB();
    const tx = db.transaction('ingredients', 'readwrite');
    const now = Date.now();
    
    await Promise.all([
      ...ingredients.map(ingredient => 
        tx.store.put({
          ...ingredient,
          lastModified: now,
          syncStatus
        })
      ),
      tx.done
    ]);
  }

  async deleteIngredient(id: string): Promise<void> {
    const db = await this.ensureDB();
    await db.delete('ingredients', id);
  }

  // Glass types operations
  async getGlassTypes(): Promise<GlassType[]> {
    const db = await this.ensureDB();
    const glassTypes = await db.getAll('glassTypes');
    return glassTypes.map(g => {
      const { lastModified, syncStatus, ...glassType } = g;
      return glassType;
    });
  }

  async getGlassType(id: string): Promise<GlassType | undefined> {
    const db = await this.ensureDB();
    const glassType = await db.get('glassTypes', id);
    if (!glassType) return undefined;
    const { lastModified, syncStatus, ...result } = glassType;
    return result;
  }

  async saveGlassType(glassType: GlassType, syncStatus: 'synced' | 'pending' = 'pending'): Promise<void> {
    const db = await this.ensureDB();
    await db.put('glassTypes', {
      ...glassType,
      lastModified: Date.now(),
      syncStatus
    });
  }

  async saveGlassTypes(glassTypes: GlassType[], syncStatus: 'synced' | 'pending' = 'synced'): Promise<void> {
    const db = await this.ensureDB();
    const tx = db.transaction('glassTypes', 'readwrite');
    const now = Date.now();
    
    await Promise.all([
      ...glassTypes.map(glassType => 
        tx.store.put({
          ...glassType,
          lastModified: now,
          syncStatus
        })
      ),
      tx.done
    ]);
  }

  async deleteGlassType(id: string): Promise<void> {
    const db = await this.ensureDB();
    await db.delete('glassTypes', id);
  }

  // Favorites operations
  async getFavorites(userId: string): Promise<string[]> {
    const db = await this.ensureDB();
    const favorites = await db.getAllFromIndex('favorites', 'userId', userId);
    return favorites.map(f => f.cocktailId);
  }

  async addFavorite(cocktailId: string, userId: string): Promise<void> {
    const db = await this.ensureDB();
    const id = `${userId}-${cocktailId}`;
    await db.put('favorites', {
      id,
      cocktailId,
      userId,
      createdAt: Date.now(),
      lastModified: Date.now(),
      syncStatus: 'pending'
    });
  }

  async removeFavorite(cocktailId: string, userId: string): Promise<void> {
    const db = await this.ensureDB();
    const id = `${userId}-${cocktailId}`;
    await db.delete('favorites', id);
  }

  async isFavorite(cocktailId: string, userId: string): Promise<boolean> {
    const db = await this.ensureDB();
    const id = `${userId}-${cocktailId}`;
    const favorite = await db.get('favorites', id);
    return !!favorite;
  }

  // Shopping list operations
  async getShoppingList(): Promise<ShoppingListItem[]> {
    const db = await this.ensureDB();
    const items = await db.getAll('shoppingList');
    return items.map(item => {
      const { lastModified, syncStatus, ...shoppingItem } = item;
      return shoppingItem;
    });
  }

  async saveShoppingList(items: ShoppingListItem[]): Promise<void> {
    const db = await this.ensureDB();
    const tx = db.transaction('shoppingList', 'readwrite');
    const now = Date.now();
    
    // Clear existing items
    await tx.store.clear();
    
    // Add new items
    await Promise.all([
      ...items.map(item => 
        tx.store.put({
          ...item,
          lastModified: now,
          syncStatus: 'pending' as const
        })
      ),
      tx.done
    ]);
  }

  // Metadata operations
  async getMetadata(key: string): Promise<any> {
    const db = await this.ensureDB();
    const metadata = await db.get('metadata', key);
    return metadata?.value;
  }

  async setMetadata(key: string, value: any): Promise<void> {
    const db = await this.ensureDB();
    await db.put('metadata', {
      key,
      value,
      lastModified: Date.now()
    });
  }

  // Sync operations
  async getPendingChanges(): Promise<{
    cocktails: any[];
    ingredients: any[];
    glassTypes: any[];
    favorites: any[];
    shoppingList: any[];
  }> {
    const db = await this.ensureDB();
    
    const [cocktails, ingredients, glassTypes, favorites, shoppingList] = await Promise.all([
      db.getAllFromIndex('cocktails', 'syncStatus', 'pending'),
      db.getAllFromIndex('ingredients', 'syncStatus', 'pending'),
      db.getAllFromIndex('glassTypes', 'syncStatus', 'pending'),
      db.getAllFromIndex('favorites', 'syncStatus', 'pending'),
      db.getAllFromIndex('shoppingList', 'syncStatus', 'pending')
    ]);

    return {
      cocktails,
      ingredients,
      glassTypes,
      favorites,
      shoppingList
    };
  }

  async markAsSynced(store: keyof TippleDB, id: string): Promise<void> {
    const db = await this.ensureDB();
    const item = await db.get(store, id as any);
    if (item) {
      await db.put(store, { ...item, syncStatus: 'synced' } as any);
    }
  }

  // Utility operations
  async clear(): Promise<void> {
    const db = await this.ensureDB();
    const tx = db.transaction(['cocktails', 'ingredients', 'glassTypes', 'favorites', 'shoppingList', 'metadata'], 'readwrite');
    
    await Promise.all([
      tx.objectStore('cocktails').clear(),
      tx.objectStore('ingredients').clear(),
      tx.objectStore('glassTypes').clear(),
      tx.objectStore('favorites').clear(),
      tx.objectStore('shoppingList').clear(),
      tx.objectStore('metadata').clear(),
      tx.done
    ]);
  }

  async getStorageInfo(): Promise<{
    cocktails: number;
    ingredients: number;
    glassTypes: number;
    favorites: number;
    shoppingList: number;
  }> {
    const db = await this.ensureDB();
    
    const [cocktails, ingredients, glassTypes, favorites, shoppingList] = await Promise.all([
      db.count('cocktails'),
      db.count('ingredients'),
      db.count('glassTypes'),
      db.count('favorites'),
      db.count('shoppingList')
    ]);

    return {
      cocktails,
      ingredients,
      glassTypes,
      favorites,
      shoppingList
    };
  }
}

// Export singleton instance
export const indexedDBStorage = new IndexedDBStorage();
