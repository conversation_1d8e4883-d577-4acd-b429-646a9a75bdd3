/**
 * Unified Storage Service - Uses hybrid storage for offline-first architecture
 * This replaces the old storage.ts with a cleaner, simpler interface
 */

'use client';

import { hybridStorage } from './hybridStorage';
import { Cocktail, Ingredient, GlassType } from '@/types/cocktail';
import { ShoppingListItem } from '@/utils/shoppingListUtils';

/**
 * Favorites Storage Service
 */
export const favoritesStorage = {
  async getFavorites(): Promise<string[]> {
    try {
      return await hybridStorage.getFavorites();
    } catch (error) {
      console.error('Error getting favorites:', error);
      return [];
    }
  },

  async addFavorite(cocktailId: string): Promise<boolean> {
    try {
      return await hybridStorage.addFavorite(cocktailId);
    } catch (error) {
      console.error('Error adding favorite:', error);
      return false;
    }
  },

  async removeFavorite(cocktailId: string): Promise<boolean> {
    try {
      return await hybridStorage.removeFavorite(cocktailId);
    } catch (error) {
      console.error('Error removing favorite:', error);
      return false;
    }
  },

  async isFavorite(cocktailId: string): Promise<boolean> {
    try {
      return await hybridStorage.isFavorite(cocktailId);
    } catch (error) {
      console.error('Error checking favorite:', error);
      return false;
    }
  }
};

/**
 * Shopping List Storage Service
 */
export const shoppingListStorage = {
  async getShoppingList(): Promise<ShoppingListItem[]> {
    try {
      return await hybridStorage.getShoppingList();
    } catch (error) {
      console.error('Error getting shopping list:', error);
      return [];
    }
  },

  async saveShoppingList(items: ShoppingListItem[]): Promise<boolean> {
    try {
      return await hybridStorage.saveShoppingList(items);
    } catch (error) {
      console.error('Error saving shopping list:', error);
      return false;
    }
  }
};

/**
 * Admin Data Storage Service (for cocktails, ingredients, glass types)
 */
export const adminDataStorage = {
  // Cocktails
  async getCocktails(): Promise<Cocktail[]> {
    try {
      return await hybridStorage.getCocktails();
    } catch (error) {
      console.error('Error getting cocktails:', error);
      return [];
    }
  },

  async getCocktail(id: string): Promise<Cocktail | undefined> {
    try {
      return await hybridStorage.getCocktail(id);
    } catch (error) {
      console.error('Error getting cocktail:', error);
      return undefined;
    }
  },

  async saveCocktail(cocktail: Cocktail): Promise<boolean> {
    try {
      return await hybridStorage.saveCocktail(cocktail);
    } catch (error) {
      console.error('Error saving cocktail:', error);
      return false;
    }
  },

  async saveCocktails(cocktails: Cocktail[]): Promise<void> {
    try {
      for (const cocktail of cocktails) {
        await hybridStorage.saveCocktail(cocktail);
      }
    } catch (error) {
      console.error('Error saving cocktails:', error);
    }
  },

  // Ingredients
  async getIngredients(): Promise<Ingredient[]> {
    try {
      return await hybridStorage.getIngredients();
    } catch (error) {
      console.error('Error getting ingredients:', error);
      return [];
    }
  },

  async getIngredient(id: string): Promise<Ingredient | undefined> {
    try {
      const ingredients = await hybridStorage.getIngredients();
      return ingredients.find(ingredient => ingredient.id === id);
    } catch (error) {
      console.error('Error getting ingredient:', error);
      return undefined;
    }
  },

  async saveIngredient(ingredient: Ingredient): Promise<boolean> {
    try {
      return await hybridStorage.saveIngredient(ingredient);
    } catch (error) {
      console.error('Error saving ingredient:', error);
      return false;
    }
  },

  async saveIngredients(ingredients: Ingredient[]): Promise<void> {
    try {
      for (const ingredient of ingredients) {
        await hybridStorage.saveIngredient(ingredient);
      }
    } catch (error) {
      console.error('Error saving ingredients:', error);
    }
  },

  // Glass Types
  async getGlassTypes(): Promise<GlassType[]> {
    try {
      return await hybridStorage.getGlassTypes();
    } catch (error) {
      console.error('Error getting glass types:', error);
      return [];
    }
  },

  async getGlassType(id: string): Promise<GlassType | undefined> {
    try {
      const glassTypes = await hybridStorage.getGlassTypes();
      return glassTypes.find(glassType => glassType.id === id);
    } catch (error) {
      console.error('Error getting glass type:', error);
      return undefined;
    }
  },

  async saveGlassType(glassType: GlassType): Promise<boolean> {
    try {
      return await hybridStorage.saveGlassType(glassType);
    } catch (error) {
      console.error('Error saving glass type:', error);
      return false;
    }
  },

  async saveGlassTypes(glassTypes: GlassType[]): Promise<void> {
    try {
      for (const glassType of glassTypes) {
        await hybridStorage.saveGlassType(glassType);
      }
    } catch (error) {
      console.error('Error saving glass types:', error);
    }
  }
};

/**
 * Utility functions for storage management
 */
export const storageUtils = {
  async getStorageInfo(): Promise<any> {
    try {
      return await hybridStorage.getStorageInfo();
    } catch (error) {
      console.error('Error getting storage info:', error);
      return null;
    }
  },

  async clearCache(): Promise<boolean> {
    try {
      await hybridStorage.clearCache();
      return true;
    } catch (error) {
      console.error('Error clearing cache:', error);
      return false;
    }
  },

  async forceSync(): Promise<boolean> {
    try {
      await hybridStorage.fullSync();
      return true;
    } catch (error) {
      console.error('Error forcing sync:', error);
      return false;
    }
  }
};

/**
 * Synchronous functions for backward compatibility
 * These use cached data and should only be used when async is not possible
 */
let cachedCocktails: Cocktail[] = [];
let cachedIngredients: Ingredient[] = [];
let cachedGlassTypes: GlassType[] = [];
let cachedFavorites: string[] = [];

// Initialize cache
if (typeof window !== 'undefined') {
  // Load initial data
  adminDataStorage.getCocktails().then(cocktails => {
    cachedCocktails = cocktails;
  });
  
  adminDataStorage.getIngredients().then(ingredients => {
    cachedIngredients = ingredients;
  });
  
  adminDataStorage.getGlassTypes().then(glassTypes => {
    cachedGlassTypes = glassTypes;
  });
  
  favoritesStorage.getFavorites().then(favorites => {
    cachedFavorites = favorites;
  });
}

export const syncStorage = {
  getCocktails: (): Cocktail[] => cachedCocktails,
  getIngredients: (): Ingredient[] => cachedIngredients,
  getGlassTypes: (): GlassType[] => cachedGlassTypes,
  getFavorites: (): string[] => cachedFavorites,
  
  getCocktail: (id: string): Cocktail | undefined => {
    return cachedCocktails.find(cocktail => cocktail.id === id);
  },
  
  getIngredient: (id: string): Ingredient | undefined => {
    return cachedIngredients.find(ingredient => ingredient.id === id);
  },
  
  getGlassType: (id: string): GlassType | undefined => {
    return cachedGlassTypes.find(glassType => glassType.id === id);
  },
  
  isFavorite: (cocktailId: string): boolean => {
    return cachedFavorites.includes(cocktailId);
  },

  // Update cache when data changes
  updateCocktailsCache: (cocktails: Cocktail[]) => {
    cachedCocktails = cocktails;
  },
  
  updateIngredientsCache: (ingredients: Ingredient[]) => {
    cachedIngredients = ingredients;
  },
  
  updateGlassTypesCache: (glassTypes: GlassType[]) => {
    cachedGlassTypes = glassTypes;
  },
  
  updateFavoritesCache: (favorites: string[]) => {
    cachedFavorites = favorites;
  }
};

// Auto-refresh cache periodically
if (typeof window !== 'undefined') {
  setInterval(async () => {
    try {
      const [cocktails, ingredients, glassTypes, favorites] = await Promise.all([
        adminDataStorage.getCocktails(),
        adminDataStorage.getIngredients(),
        adminDataStorage.getGlassTypes(),
        favoritesStorage.getFavorites()
      ]);
      
      syncStorage.updateCocktailsCache(cocktails);
      syncStorage.updateIngredientsCache(ingredients);
      syncStorage.updateGlassTypesCache(glassTypes);
      syncStorage.updateFavoritesCache(favorites);
    } catch (error) {
      console.warn('Failed to refresh cache:', error);
    }
  }, 30000); // Refresh every 30 seconds
}
