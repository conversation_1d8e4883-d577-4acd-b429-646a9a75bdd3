/**
 * Data transformation layer for converting between Supabase and local TypeScript formats
 * Handles all data structure conversions and ensures type safety
 */

import { 
  Cocktail, 
  Ingredient, 
  GlassType, 
  CocktailIngredient,
  IngredientCategory,
  CocktailCategory,
  Difficulty 
} from '@/types/cocktail';
import { Database } from './supabase';

// Type aliases for Supabase row types
type SupabaseCocktail = Database['public']['Tables']['cocktails']['Row'];
type SupabaseIngredient = Database['public']['Tables']['ingredients']['Row'];
type SupabaseGlassType = Database['public']['Tables']['glass_types']['Row'];

/**
 * Transform Supabase ingredient to local Ingredient interface
 */
export function transformSupabaseIngredient(supabaseIngredient: SupabaseIngredient): Ingredient {
  return {
    id: supabaseIngredient.id,
    name: supabaseIngredient.name,
    category: supabaseIngredient.category as IngredientCategory,
    alcoholic: supabaseIngredient.alcoholic,
    description: supabaseIngredient.description || undefined,
    abv: supabaseIngredient.abv || undefined
  };
}

/**
 * Transform local Ingredient to Supabase format
 */
export function transformIngredientToSupabase(ingredient: Ingredient): Database['public']['Tables']['ingredients']['Insert'] {
  return {
    id: ingredient.id,
    name: ingredient.name,
    category: ingredient.category,
    alcoholic: ingredient.alcoholic,
    description: ingredient.description || null,
    abv: ingredient.abv || null
  };
}

/**
 * Transform Supabase glass type to local GlassType interface
 */
export function transformSupabaseGlassType(supabaseGlassType: SupabaseGlassType): GlassType {
  return {
    id: supabaseGlassType.id,
    name: supabaseGlassType.name,
    description: supabaseGlassType.description,
    iconUrl: supabaseGlassType.icon_url || undefined,
    capacity: supabaseGlassType.capacity || undefined
  };
}

/**
 * Transform local GlassType to Supabase format
 */
export function transformGlassTypeToSupabase(glassType: GlassType): Database['public']['Tables']['glass_types']['Insert'] {
  return {
    id: glassType.id,
    name: glassType.name,
    description: glassType.description,
    icon_url: glassType.iconUrl || null,
    capacity: glassType.capacity || null
  };
}

/**
 * Validate and transform JSONB cocktail ingredients from Supabase
 */
export function transformSupabaseCocktailIngredients(ingredientsData: unknown): CocktailIngredient[] {
  if (!Array.isArray(ingredientsData)) {
    console.warn('Invalid ingredients data format, expected array:', ingredientsData);
    return [];
  }

  return ingredientsData.map((item: any, index: number) => {
    try {
      // Validate the structure
      if (!item || typeof item !== 'object') {
        throw new Error(`Invalid ingredient item at index ${index}`);
      }

      if (!item.ingredient || typeof item.ingredient !== 'object') {
        throw new Error(`Invalid ingredient object at index ${index}`);
      }

      if (!item.amount || typeof item.amount !== 'string') {
        throw new Error(`Invalid amount at index ${index}`);
      }

      // Transform the nested ingredient
      const ingredient: Ingredient = {
        id: item.ingredient.id || `unknown-${index}`,
        name: item.ingredient.name || 'Unknown Ingredient',
        category: item.ingredient.category || IngredientCategory.OTHER,
        alcoholic: Boolean(item.ingredient.alcoholic),
        description: item.ingredient.description || undefined,
        abv: typeof item.ingredient.abv === 'number' ? item.ingredient.abv : undefined
      };

      // Create the cocktail ingredient
      const cocktailIngredient: CocktailIngredient = {
        ingredient,
        amount: item.amount,
        optional: Boolean(item.optional),
        garnish: Boolean(item.garnish)
      };

      return cocktailIngredient;
    } catch (error) {
      console.error(`Error transforming ingredient at index ${index}:`, error, item);
      // Return a fallback ingredient to prevent app crashes
      return {
        ingredient: {
          id: `error-${index}`,
          name: 'Invalid Ingredient',
          category: IngredientCategory.OTHER,
          alcoholic: false
        },
        amount: '1 unit'
      };
    }
  });
}

/**
 * Transform local CocktailIngredient array to Supabase JSONB format
 */
export function transformCocktailIngredientsToSupabase(ingredients: CocktailIngredient[]): unknown[] {
  return ingredients.map(ci => ({
    ingredient: {
      id: ci.ingredient.id,
      name: ci.ingredient.name,
      category: ci.ingredient.category,
      alcoholic: ci.ingredient.alcoholic,
      description: ci.ingredient.description || null,
      abv: ci.ingredient.abv || null
    },
    amount: ci.amount,
    optional: ci.optional || false,
    garnish: ci.garnish || false
  }));
}

/**
 * Validate and transform JSONB glass type data from Supabase
 */
export function transformSupabaseGlassTypeData(glassTypeData: unknown): GlassType {
  if (!glassTypeData || typeof glassTypeData !== 'object') {
    console.warn('Invalid glass type data format:', glassTypeData);
    return {
      id: 'unknown',
      name: 'Unknown Glass',
      description: 'Glass type not specified'
    };
  }

  const data = glassTypeData as any;
  
  return {
    id: data.id || 'unknown',
    name: data.name || 'Unknown Glass',
    description: data.description || 'Glass type not specified',
    iconUrl: data.icon_url || data.iconUrl || undefined,
    capacity: data.capacity || undefined
  };
}

/**
 * Transform Supabase cocktail to local Cocktail interface
 */
export function transformSupabaseCocktail(supabaseCocktail: SupabaseCocktail): Cocktail {
  try {
    const ingredients = transformSupabaseCocktailIngredients(supabaseCocktail.ingredients);
    const glassType = transformSupabaseGlassTypeData(supabaseCocktail.glass_type_data);

    return {
      id: supabaseCocktail.id,
      name: supabaseCocktail.name,
      description: supabaseCocktail.description,
      instructions: supabaseCocktail.instructions,
      ingredients,
      glassType,
      category: supabaseCocktail.category as CocktailCategory,
      tags: supabaseCocktail.tags,
      difficulty: supabaseCocktail.difficulty as Difficulty,
      prepTime: supabaseCocktail.prep_time,
      servings: supabaseCocktail.servings,
      imageUrl: supabaseCocktail.image_url || undefined,
      garnish: supabaseCocktail.garnish || undefined,
      history: supabaseCocktail.history || undefined,
      variations: supabaseCocktail.variations || undefined
    };
  } catch (error) {
    console.error('Error transforming Supabase cocktail:', error, supabaseCocktail);
    // Return a fallback cocktail to prevent app crashes
    return {
      id: supabaseCocktail.id || 'error',
      name: supabaseCocktail.name || 'Invalid Cocktail',
      description: supabaseCocktail.description || 'Cocktail data is corrupted',
      instructions: Array.isArray(supabaseCocktail.instructions) ? supabaseCocktail.instructions : ['Instructions unavailable'],
      ingredients: [],
      glassType: {
        id: 'unknown',
        name: 'Unknown Glass',
        description: 'Glass type not specified'
      },
      category: CocktailCategory.CLASSIC,
      tags: Array.isArray(supabaseCocktail.tags) ? supabaseCocktail.tags : [],
      difficulty: Difficulty.MEDIUM,
      prepTime: supabaseCocktail.prep_time || 5,
      servings: supabaseCocktail.servings || 1
    };
  }
}

/**
 * Transform local Cocktail to Supabase format
 */
export function transformCocktailToSupabase(cocktail: Cocktail): Database['public']['Tables']['cocktails']['Insert'] {
  return {
    id: cocktail.id,
    name: cocktail.name,
    description: cocktail.description,
    instructions: cocktail.instructions,
    ingredients: transformCocktailIngredientsToSupabase(cocktail.ingredients),
    glass_type_data: {
      id: cocktail.glassType.id,
      name: cocktail.glassType.name,
      description: cocktail.glassType.description,
      icon_url: cocktail.glassType.iconUrl || null,
      capacity: cocktail.glassType.capacity || null
    },
    category: cocktail.category,
    difficulty: cocktail.difficulty,
    prep_time: cocktail.prepTime,
    servings: cocktail.servings,
    garnish: cocktail.garnish || null,
    tags: cocktail.tags,
    image_url: cocktail.imageUrl || null,
    history: cocktail.history || null,
    variations: cocktail.variations || null
  };
}

/**
 * Batch transform arrays of data
 */
export const batchTransform = {
  supabaseIngredientsToLocal: (supabaseIngredients: SupabaseIngredient[]): Ingredient[] => {
    return supabaseIngredients.map(transformSupabaseIngredient);
  },

  localIngredientsToSupabase: (ingredients: Ingredient[]): Database['public']['Tables']['ingredients']['Insert'][] => {
    return ingredients.map(transformIngredientToSupabase);
  },

  supabaseGlassTypesToLocal: (supabaseGlassTypes: SupabaseGlassType[]): GlassType[] => {
    return supabaseGlassTypes.map(transformSupabaseGlassType);
  },

  localGlassTypesToSupabase: (glassTypes: GlassType[]): Database['public']['Tables']['glass_types']['Insert'][] => {
    return glassTypes.map(transformGlassTypeToSupabase);
  },

  supabaseCocktailsToLocal: (supabaseCocktails: SupabaseCocktail[]): Cocktail[] => {
    return supabaseCocktails.map(transformSupabaseCocktail);
  },

  localCocktailsToSupabase: (cocktails: Cocktail[]): Database['public']['Tables']['cocktails']['Insert'][] => {
    return cocktails.map(transformCocktailToSupabase);
  }
};

/**
 * Data validation utilities
 */
export const validateData = {
  ingredient: (data: any): data is Ingredient => {
    return data && 
           typeof data.id === 'string' &&
           typeof data.name === 'string' &&
           typeof data.category === 'string' &&
           typeof data.alcoholic === 'boolean';
  },

  glassType: (data: any): data is GlassType => {
    return data &&
           typeof data.id === 'string' &&
           typeof data.name === 'string' &&
           typeof data.description === 'string';
  },

  cocktail: (data: any): data is Cocktail => {
    return data &&
           typeof data.id === 'string' &&
           typeof data.name === 'string' &&
           typeof data.description === 'string' &&
           Array.isArray(data.instructions) &&
           Array.isArray(data.ingredients) &&
           validateData.glassType(data.glassType);
  }
};
