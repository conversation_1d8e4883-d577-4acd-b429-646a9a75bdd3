/**
 * Hybrid Storage Service - Seamlessly handles Supabase (online) and IndexedDB (offline)
 * Implements offline-first architecture with automatic synchronization
 */

import { supabase } from './supabase';
import { indexedDBStorage } from './indexedDB';
import { 
  transformSupabaseCocktail, 
  transformCocktailToSupabase,
  transformSupabaseIngredient,
  transformIngredientToSupabase,
  transformSupabaseGlassType,
  transformGlassTypeToSupabase,
  batchTransform
} from './dataTransform';
import { Cocktail, Ingredient, GlassType } from '@/types/cocktail';
import { ShoppingListItem } from '@/utils/shoppingListUtils';

// Connection status
let isOnline = typeof navigator !== 'undefined' ? navigator.onLine : false;
let syncInProgress = false;

// Update online status
if (typeof window !== 'undefined') {
  window.addEventListener('online', () => {
    isOnline = true;
    console.log('Connection restored - triggering sync');
    triggerSync();
  });
  
  window.addEventListener('offline', () => {
    isOnline = false;
    console.log('Connection lost - switching to offline mode');
  });
}

/**
 * Check if Supabase is available and we're online
 */
async function isSupabaseAvailable(): Promise<boolean> {
  if (!isOnline) return false;
  
  try {
    const { error } = await supabase.from('cocktails').select('id').limit(1);
    return !error;
  } catch {
    return false;
  }
}

/**
 * Get current user ID (anonymous or authenticated)
 */
async function getCurrentUserId(): Promise<string> {
  const { data: { user } } = await supabase.auth.getUser();
  
  if (user) {
    return user.id;
  }
  
  // Create or get anonymous user session
  let anonymousId = await indexedDBStorage.getMetadata('anonymousUserId');
  if (!anonymousId) {
    anonymousId = `anon_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    await indexedDBStorage.setMetadata('anonymousUserId', anonymousId);
  }
  
  return anonymousId;
}

/**
 * Hybrid Storage Service Class
 */
export class HybridStorageService {
  private initialized = false;

  async init(): Promise<void> {
    if (this.initialized) return;
    
    try {
      await indexedDBStorage.init();
      this.initialized = true;
      console.log('Hybrid storage initialized');
      
      // Trigger initial sync if online
      if (isOnline) {
        triggerSync();
      }
    } catch (error) {
      console.error('Failed to initialize hybrid storage:', error);
      throw error;
    }
  }

  private async ensureInitialized(): Promise<void> {
    if (!this.initialized) {
      await this.init();
    }
  }

  // Cocktails operations
  async getCocktails(): Promise<Cocktail[]> {
    await this.ensureInitialized();
    
    try {
      // Always try IndexedDB first (offline-first)
      const localCocktails = await indexedDBStorage.getCocktails();
      
      // If we have local data, return it immediately
      if (localCocktails.length > 0) {
        // Optionally sync in background if online
        if (isOnline && !syncInProgress) {
          this.syncCocktailsInBackground();
        }
        return localCocktails;
      }
      
      // If no local data and we're online, fetch from Supabase
      if (await isSupabaseAvailable()) {
        const { data, error } = await supabase
          .from('cocktails')
          .select('*')
          .order('name');
        
        if (!error && data) {
          const cocktails = batchTransform.supabaseCocktailsToLocal(data);
          // Cache in IndexedDB
          await indexedDBStorage.saveCocktails(cocktails, 'synced');
          return cocktails;
        }
      }
      
      // Fallback to empty array
      return [];
    } catch (error) {
      console.error('Error getting cocktails:', error);
      // Always return local data as fallback
      return await indexedDBStorage.getCocktails();
    }
  }

  async getCocktail(id: string): Promise<Cocktail | undefined> {
    await this.ensureInitialized();
    
    try {
      // Check IndexedDB first
      const localCocktail = await indexedDBStorage.getCocktail(id);
      if (localCocktail) {
        return localCocktail;
      }
      
      // If not found locally and online, try Supabase
      if (await isSupabaseAvailable()) {
        const { data, error } = await supabase
          .from('cocktails')
          .select('*')
          .eq('id', id)
          .single();
        
        if (!error && data) {
          const cocktail = transformSupabaseCocktail(data);
          // Cache in IndexedDB
          await indexedDBStorage.saveCocktail(cocktail, 'synced');
          return cocktail;
        }
      }
      
      return undefined;
    } catch (error) {
      console.error('Error getting cocktail:', error);
      return await indexedDBStorage.getCocktail(id);
    }
  }

  async saveCocktail(cocktail: Cocktail): Promise<boolean> {
    await this.ensureInitialized();
    
    try {
      // Always save to IndexedDB first
      await indexedDBStorage.saveCocktail(cocktail, 'pending');
      
      // Try to sync to Supabase if online
      if (await isSupabaseAvailable()) {
        const supabaseCocktail = transformCocktailToSupabase(cocktail);
        const { error } = await supabase
          .from('cocktails')
          .upsert(supabaseCocktail);
        
        if (!error) {
          // Mark as synced
          await indexedDBStorage.saveCocktail(cocktail, 'synced');
          return true;
        }
      }
      
      // Even if sync failed, we saved locally
      return true;
    } catch (error) {
      console.error('Error saving cocktail:', error);
      return false;
    }
  }

  // Ingredients operations
  async getIngredients(): Promise<Ingredient[]> {
    await this.ensureInitialized();
    
    try {
      const localIngredients = await indexedDBStorage.getIngredients();
      
      if (localIngredients.length > 0) {
        if (isOnline && !syncInProgress) {
          this.syncIngredientsInBackground();
        }
        return localIngredients;
      }
      
      if (await isSupabaseAvailable()) {
        const { data, error } = await supabase
          .from('ingredients')
          .select('*')
          .order('name');
        
        if (!error && data) {
          const ingredients = batchTransform.supabaseIngredientsToLocal(data);
          await indexedDBStorage.saveIngredients(ingredients, 'synced');
          return ingredients;
        }
      }
      
      return [];
    } catch (error) {
      console.error('Error getting ingredients:', error);
      return await indexedDBStorage.getIngredients();
    }
  }

  async saveIngredient(ingredient: Ingredient): Promise<boolean> {
    await this.ensureInitialized();
    
    try {
      await indexedDBStorage.saveIngredient(ingredient, 'pending');
      
      if (await isSupabaseAvailable()) {
        const supabaseIngredient = transformIngredientToSupabase(ingredient);
        const { error } = await supabase
          .from('ingredients')
          .upsert(supabaseIngredient);
        
        if (!error) {
          await indexedDBStorage.saveIngredient(ingredient, 'synced');
          return true;
        }
      }
      
      return true;
    } catch (error) {
      console.error('Error saving ingredient:', error);
      return false;
    }
  }

  // Glass types operations
  async getGlassTypes(): Promise<GlassType[]> {
    await this.ensureInitialized();
    
    try {
      const localGlassTypes = await indexedDBStorage.getGlassTypes();
      
      if (localGlassTypes.length > 0) {
        if (isOnline && !syncInProgress) {
          this.syncGlassTypesInBackground();
        }
        return localGlassTypes;
      }
      
      if (await isSupabaseAvailable()) {
        const { data, error } = await supabase
          .from('glass_types')
          .select('*')
          .order('name');
        
        if (!error && data) {
          const glassTypes = batchTransform.supabaseGlassTypesToLocal(data);
          await indexedDBStorage.saveGlassTypes(glassTypes, 'synced');
          return glassTypes;
        }
      }
      
      return [];
    } catch (error) {
      console.error('Error getting glass types:', error);
      return await indexedDBStorage.getGlassTypes();
    }
  }

  async saveGlassType(glassType: GlassType): Promise<boolean> {
    await this.ensureInitialized();
    
    try {
      await indexedDBStorage.saveGlassType(glassType, 'pending');
      
      if (await isSupabaseAvailable()) {
        const supabaseGlassType = transformGlassTypeToSupabase(glassType);
        const { error } = await supabase
          .from('glass_types')
          .upsert(supabaseGlassType);
        
        if (!error) {
          await indexedDBStorage.saveGlassType(glassType, 'synced');
          return true;
        }
      }
      
      return true;
    } catch (error) {
      console.error('Error saving glass type:', error);
      return false;
    }
  }

  // Favorites operations
  async getFavorites(): Promise<string[]> {
    await this.ensureInitialized();
    
    try {
      const userId = await getCurrentUserId();
      const localFavorites = await indexedDBStorage.getFavorites(userId);
      
      // Try to sync with Supabase if online and user is authenticated
      if (await isSupabaseAvailable() && userId.startsWith('anon_') === false) {
        const { data, error } = await supabase
          .from('user_favorites')
          .select('cocktail_id')
          .eq('user_id', userId);
        
        if (!error && data) {
          const supabaseFavorites = data.map(f => f.cocktail_id);
          // Merge and deduplicate
          const allFavorites = [...new Set([...localFavorites, ...supabaseFavorites])];
          
          // Update local storage with merged data
          for (const cocktailId of allFavorites) {
            if (!localFavorites.includes(cocktailId)) {
              await indexedDBStorage.addFavorite(cocktailId, userId);
            }
          }
          
          return allFavorites;
        }
      }
      
      return localFavorites;
    } catch (error) {
      console.error('Error getting favorites:', error);
      const userId = await getCurrentUserId();
      return await indexedDBStorage.getFavorites(userId);
    }
  }

  async addFavorite(cocktailId: string): Promise<boolean> {
    await this.ensureInitialized();
    
    try {
      const userId = await getCurrentUserId();
      await indexedDBStorage.addFavorite(cocktailId, userId);
      
      // Sync to Supabase if online and user is authenticated
      if (await isSupabaseAvailable() && !userId.startsWith('anon_')) {
        const { error } = await supabase
          .from('user_favorites')
          .insert({ user_id: userId, cocktail_id: cocktailId });
        
        if (error) {
          console.warn('Failed to sync favorite to Supabase:', error);
        }
      }
      
      return true;
    } catch (error) {
      console.error('Error adding favorite:', error);
      return false;
    }
  }

  async removeFavorite(cocktailId: string): Promise<boolean> {
    await this.ensureInitialized();
    
    try {
      const userId = await getCurrentUserId();
      await indexedDBStorage.removeFavorite(cocktailId, userId);
      
      // Sync to Supabase if online and user is authenticated
      if (await isSupabaseAvailable() && !userId.startsWith('anon_')) {
        const { error } = await supabase
          .from('user_favorites')
          .delete()
          .eq('user_id', userId)
          .eq('cocktail_id', cocktailId);
        
        if (error) {
          console.warn('Failed to remove favorite from Supabase:', error);
        }
      }
      
      return true;
    } catch (error) {
      console.error('Error removing favorite:', error);
      return false;
    }
  }

  async isFavorite(cocktailId: string): Promise<boolean> {
    await this.ensureInitialized();
    
    try {
      const userId = await getCurrentUserId();
      return await indexedDBStorage.isFavorite(cocktailId, userId);
    } catch (error) {
      console.error('Error checking favorite:', error);
      return false;
    }
  }

  // Shopping list operations
  async getShoppingList(): Promise<ShoppingListItem[]> {
    await this.ensureInitialized();
    
    try {
      const localShoppingList = await indexedDBStorage.getShoppingList();
      
      // Try to sync with Supabase if online and user is authenticated
      const userId = await getCurrentUserId();
      if (await isSupabaseAvailable() && !userId.startsWith('anon_')) {
        const { data, error } = await supabase
          .from('user_shopping_list')
          .select('*')
          .eq('user_id', userId);
        
        if (!error && data) {
          // Transform Supabase data to local format
          const supabaseShoppingList: ShoppingListItem[] = data.map(item => {
            // Try to find the full ingredient data
            const ingredient = await indexedDBStorage.getIngredient(item.ingredient_id);
            return {
              ingredient: ingredient || {
                id: item.ingredient_id,
                name: item.ingredient_id,
                category: 'other' as any,
                alcoholic: false
              },
              amount: item.amount,
              cocktails: item.cocktails
            };
          });
          
          // For now, prefer local data but this could be enhanced with conflict resolution
          return localShoppingList.length > 0 ? localShoppingList : supabaseShoppingList;
        }
      }
      
      return localShoppingList;
    } catch (error) {
      console.error('Error getting shopping list:', error);
      return await indexedDBStorage.getShoppingList();
    }
  }

  async saveShoppingList(items: ShoppingListItem[]): Promise<boolean> {
    await this.ensureInitialized();
    
    try {
      await indexedDBStorage.saveShoppingList(items);
      
      // Sync to Supabase if online and user is authenticated
      const userId = await getCurrentUserId();
      if (await isSupabaseAvailable() && !userId.startsWith('anon_')) {
        // Clear existing items
        await supabase
          .from('user_shopping_list')
          .delete()
          .eq('user_id', userId);
        
        // Insert new items
        if (items.length > 0) {
          const supabaseItems = items.map(item => ({
            user_id: userId,
            ingredient_id: item.id,
            amount: item.amount,
            cocktails: item.cocktails
          }));
          
          const { error } = await supabase
            .from('user_shopping_list')
            .insert(supabaseItems);
          
          if (error) {
            console.warn('Failed to sync shopping list to Supabase:', error);
          }
        }
      }
      
      return true;
    } catch (error) {
      console.error('Error saving shopping list:', error);
      return false;
    }
  }

  // Background sync methods
  private async syncCocktailsInBackground(): Promise<void> {
    try {
      const { data, error } = await supabase
        .from('cocktails')
        .select('*')
        .order('name');
      
      if (!error && data) {
        const cocktails = batchTransform.supabaseCocktailsToLocal(data);
        await indexedDBStorage.saveCocktails(cocktails, 'synced');
        console.log('Background sync: cocktails updated');
      }
    } catch (error) {
      console.warn('Background sync failed for cocktails:', error);
    }
  }

  private async syncIngredientsInBackground(): Promise<void> {
    try {
      const { data, error } = await supabase
        .from('ingredients')
        .select('*')
        .order('name');
      
      if (!error && data) {
        const ingredients = batchTransform.supabaseIngredientsToLocal(data);
        await indexedDBStorage.saveIngredients(ingredients, 'synced');
        console.log('Background sync: ingredients updated');
      }
    } catch (error) {
      console.warn('Background sync failed for ingredients:', error);
    }
  }

  private async syncGlassTypesInBackground(): Promise<void> {
    try {
      const { data, error } = await supabase
        .from('glass_types')
        .select('*')
        .order('name');
      
      if (!error && data) {
        const glassTypes = batchTransform.supabaseGlassTypesToLocal(data);
        await indexedDBStorage.saveGlassTypes(glassTypes, 'synced');
        console.log('Background sync: glass types updated');
      }
    } catch (error) {
      console.warn('Background sync failed for glass types:', error);
    }
  }

  // Full sync operation
  async fullSync(): Promise<void> {
    if (syncInProgress || !isOnline) return;
    
    syncInProgress = true;
    console.log('Starting full sync...');
    
    try {
      await Promise.all([
        this.syncCocktailsInBackground(),
        this.syncIngredientsInBackground(),
        this.syncGlassTypesInBackground()
      ]);
      
      console.log('Full sync completed successfully');
    } catch (error) {
      console.error('Full sync failed:', error);
    } finally {
      syncInProgress = false;
    }
  }

  // Utility methods
  async getStorageInfo(): Promise<any> {
    await this.ensureInitialized();
    return await indexedDBStorage.getStorageInfo();
  }

  async clearCache(): Promise<void> {
    await this.ensureInitialized();
    await indexedDBStorage.clear();
  }
}

// Export singleton instance
export const hybridStorage = new HybridStorageService();

// Trigger sync function
async function triggerSync(): Promise<void> {
  if (!syncInProgress) {
    setTimeout(() => hybridStorage.fullSync(), 1000); // Delay to avoid immediate sync on connection restore
  }
}

// Initialize hybrid storage on module load
if (typeof window !== 'undefined') {
  hybridStorage.init().catch(console.error);
}
