'use client';

import React, { useState, useEffect } from 'react';
import { adminDataStorage, favoritesStorage, shoppingListStorage, storageUtils } from '@/lib/storageService';
import { getCurrentUser, onAuthStateChange } from '@/lib/auth';
import { Cocktail, Ingredient, GlassType } from '@/types/cocktail';
import { ShoppingListItem } from '@/utils/shoppingListUtils';

export default function TestHybridPage() {
  const [testResults, setTestResults] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [storageInfo, setStorageInfo] = useState<any>(null);
  const [currentUser, setCurrentUser] = useState<any>(null);

  useEffect(() => {
    const unsubscribe = onAuthStateChange((authState) => {
      setCurrentUser(authState.user);
    });
    return unsubscribe;
  }, []);

  const addTestResult = (message: string, success: boolean = true) => {
    const timestamp = new Date().toLocaleTimeString();
    const status = success ? '✅' : '❌';
    setTestResults(prev => [...prev, `${timestamp} ${status} ${message}`]);
  };

  const loadStorageInfo = async () => {
    const info = await storageUtils.getStorageInfo();
    setStorageInfo(info);
  };

  const testDataRetrieval = async () => {
    addTestResult('Testing data retrieval...');
    
    try {
      // Test cocktails
      const cocktails = await adminDataStorage.getCocktails();
      addTestResult(`Retrieved ${cocktails.length} cocktails`);
      
      // Test ingredients
      const ingredients = await adminDataStorage.getIngredients();
      addTestResult(`Retrieved ${ingredients.length} ingredients`);
      
      // Test glass types
      const glassTypes = await adminDataStorage.getGlassTypes();
      addTestResult(`Retrieved ${glassTypes.length} glass types`);
      
      // Test favorites
      const favorites = await favoritesStorage.getFavorites();
      addTestResult(`Retrieved ${favorites.length} favorites`);
      
      // Test shopping list
      const shoppingList = await shoppingListStorage.getShoppingList();
      addTestResult(`Retrieved ${shoppingList.length} shopping list items`);
      
    } catch (error) {
      addTestResult(`Data retrieval failed: ${error}`, false);
    }
  };

  const testFavoritesOperations = async () => {
    addTestResult('Testing favorites operations...');
    
    try {
      const testCocktailId = 'test-cocktail-' + Date.now();
      
      // Add favorite
      const addResult = await favoritesStorage.addFavorite(testCocktailId);
      addTestResult(`Add favorite: ${addResult ? 'Success' : 'Failed'}`, addResult);
      
      // Check if favorite
      const isFav = await favoritesStorage.isFavorite(testCocktailId);
      addTestResult(`Check favorite: ${isFav ? 'Found' : 'Not found'}`, isFav);
      
      // Remove favorite
      const removeResult = await favoritesStorage.removeFavorite(testCocktailId);
      addTestResult(`Remove favorite: ${removeResult ? 'Success' : 'Failed'}`, removeResult);
      
      // Verify removal
      const isStillFav = await favoritesStorage.isFavorite(testCocktailId);
      addTestResult(`Verify removal: ${!isStillFav ? 'Success' : 'Failed'}`, !isStillFav);
      
    } catch (error) {
      addTestResult(`Favorites test failed: ${error}`, false);
    }
  };

  const testShoppingListOperations = async () => {
    addTestResult('Testing shopping list operations...');
    
    try {
      const testItem: ShoppingListItem = {
        ingredient: {
          id: 'test-ingredient-' + Date.now(),
          name: 'Test Ingredient',
          category: 'spirit' as any,
          alcoholic: true
        },
        amount: '2 oz',
        cocktails: ['Test Cocktail']
      };
      
      // Get current list
      const currentList = await shoppingListStorage.getShoppingList();
      addTestResult(`Current shopping list has ${currentList.length} items`);
      
      // Add test item
      const newList = [...currentList, testItem];
      const saveResult = await shoppingListStorage.saveShoppingList(newList);
      addTestResult(`Save shopping list: ${saveResult ? 'Success' : 'Failed'}`, saveResult);
      
      // Verify addition
      const updatedList = await shoppingListStorage.getShoppingList();
      const itemFound = updatedList.some(item => item.ingredient.id === testItem.ingredient.id);
      addTestResult(`Verify addition: ${itemFound ? 'Found' : 'Not found'}`, itemFound);
      
      // Remove test item
      const finalList = updatedList.filter(item => item.ingredient.id !== testItem.ingredient.id);
      const removeResult = await shoppingListStorage.saveShoppingList(finalList);
      addTestResult(`Remove test item: ${removeResult ? 'Success' : 'Failed'}`, removeResult);
      
    } catch (error) {
      addTestResult(`Shopping list test failed: ${error}`, false);
    }
  };

  const testOfflineMode = async () => {
    addTestResult('Testing offline mode simulation...');
    
    try {
      // Simulate offline by temporarily disabling network
      addTestResult('Note: Offline mode testing requires manual network disconnection');
      
      // Test data retrieval in offline mode
      const cocktails = await adminDataStorage.getCocktails();
      addTestResult(`Offline cocktails retrieval: ${cocktails.length > 0 ? 'Success' : 'No data'}`);
      
      // Test favorites in offline mode
      const favorites = await favoritesStorage.getFavorites();
      addTestResult(`Offline favorites retrieval: Success (${favorites.length} items)`);
      
    } catch (error) {
      addTestResult(`Offline test failed: ${error}`, false);
    }
  };

  const testDataConsistency = async () => {
    addTestResult('Testing data consistency...');
    
    try {
      // Test multiple reads return same data
      const cocktails1 = await adminDataStorage.getCocktails();
      const cocktails2 = await adminDataStorage.getCocktails();
      
      const consistent = cocktails1.length === cocktails2.length;
      addTestResult(`Data consistency: ${consistent ? 'Consistent' : 'Inconsistent'}`, consistent);
      
      // Test specific cocktail retrieval
      if (cocktails1.length > 0) {
        const firstCocktail = cocktails1[0];
        const retrievedCocktail = await adminDataStorage.getCocktail(firstCocktail.id);
        const matches = retrievedCocktail?.id === firstCocktail.id;
        addTestResult(`Individual cocktail retrieval: ${matches ? 'Success' : 'Failed'}`, matches);
      }
      
    } catch (error) {
      addTestResult(`Consistency test failed: ${error}`, false);
    }
  };

  const runAllTests = async () => {
    setIsLoading(true);
    setTestResults([]);
    
    addTestResult('Starting hybrid storage tests...');
    
    await testDataRetrieval();
    await testFavoritesOperations();
    await testShoppingListOperations();
    await testDataConsistency();
    await testOfflineMode();
    
    addTestResult('All tests completed!');
    await loadStorageInfo();
    setIsLoading(false);
  };

  const clearCache = async () => {
    const result = await storageUtils.clearCache();
    addTestResult(`Cache cleared: ${result ? 'Success' : 'Failed'}`, result);
    await loadStorageInfo();
  };

  const forceSync = async () => {
    const result = await storageUtils.forceSync();
    addTestResult(`Force sync: ${result ? 'Success' : 'Failed'}`, result);
    await loadStorageInfo();
  };

  useEffect(() => {
    loadStorageInfo();
  }, []);

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="bg-white rounded-lg shadow-lg p-6">
          <h1 className="text-3xl font-bold text-gray-900 mb-6">
            Hybrid Storage Architecture Test
          </h1>
          
          {/* User Info */}
          <div className="mb-6 p-4 bg-blue-50 rounded-lg">
            <h2 className="text-lg font-semibold text-blue-900 mb-2">Current User</h2>
            <div className="text-sm text-blue-800">
              <p>ID: {currentUser?.id || 'Not loaded'}</p>
              <p>Type: {currentUser?.isAnonymous ? 'Anonymous' : 'Authenticated'}</p>
              <p>Email: {currentUser?.email || 'N/A'}</p>
              <p>Admin: {currentUser?.isAdmin ? 'Yes' : 'No'}</p>
            </div>
          </div>

          {/* Storage Info */}
          {storageInfo && (
            <div className="mb-6 p-4 bg-green-50 rounded-lg">
              <h2 className="text-lg font-semibold text-green-900 mb-2">Storage Information</h2>
              <div className="grid grid-cols-2 md:grid-cols-5 gap-4 text-sm text-green-800">
                <div>
                  <p className="font-medium">Cocktails</p>
                  <p>{storageInfo.cocktails}</p>
                </div>
                <div>
                  <p className="font-medium">Ingredients</p>
                  <p>{storageInfo.ingredients}</p>
                </div>
                <div>
                  <p className="font-medium">Glass Types</p>
                  <p>{storageInfo.glassTypes}</p>
                </div>
                <div>
                  <p className="font-medium">Favorites</p>
                  <p>{storageInfo.favorites}</p>
                </div>
                <div>
                  <p className="font-medium">Shopping List</p>
                  <p>{storageInfo.shoppingList}</p>
                </div>
              </div>
            </div>
          )}

          {/* Test Controls */}
          <div className="mb-6 flex flex-wrap gap-4">
            <button
              onClick={runAllTests}
              disabled={isLoading}
              className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 disabled:opacity-50"
            >
              {isLoading ? 'Running Tests...' : 'Run All Tests'}
            </button>
            <button
              onClick={forceSync}
              className="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700"
            >
              Force Sync
            </button>
            <button
              onClick={clearCache}
              className="bg-red-600 text-white px-4 py-2 rounded-md hover:bg-red-700"
            >
              Clear Cache
            </button>
            <button
              onClick={loadStorageInfo}
              className="bg-gray-600 text-white px-4 py-2 rounded-md hover:bg-gray-700"
            >
              Refresh Info
            </button>
          </div>

          {/* Test Results */}
          <div className="bg-gray-900 text-green-400 p-4 rounded-lg font-mono text-sm">
            <h2 className="text-white text-lg font-semibold mb-4">Test Results</h2>
            <div className="space-y-1 max-h-96 overflow-y-auto">
              {testResults.length === 0 ? (
                <p className="text-gray-400">No tests run yet. Click "Run All Tests" to start.</p>
              ) : (
                testResults.map((result, index) => (
                  <div key={index} className="whitespace-pre-wrap">
                    {result}
                  </div>
                ))
              )}
            </div>
          </div>

          {/* Instructions */}
          <div className="mt-6 p-4 bg-yellow-50 rounded-lg">
            <h2 className="text-lg font-semibold text-yellow-900 mb-2">Testing Instructions</h2>
            <div className="text-sm text-yellow-800 space-y-2">
              <p>1. Run tests with network connection to test online mode</p>
              <p>2. Disconnect network and run tests again to test offline mode</p>
              <p>3. Reconnect network and check if data syncs properly</p>
              <p>4. Try signing in/out to test user data migration</p>
              <p>5. Check browser DevTools → Application → IndexedDB to see stored data</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
