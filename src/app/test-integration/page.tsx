'use client';

import { useState } from 'react';
import { favoritesStorage, shoppingListStorage, adminDataStorage } from '@/lib/storage';
import { supabase } from '@/lib/supabase';
import { IngredientCategory } from '@/types/cocktail';

export default function TestIntegrationPage() {
  const [testResults, setTestResults] = useState<string[]>([]);
  const [isRunning, setIsRunning] = useState(false);

  const addResult = (message: string) => {
    setTestResults(prev => [...prev, `${new Date().toLocaleTimeString()}: ${message}`]);
  };

  const runTests = async () => {
    setIsRunning(true);
    setTestResults([]);
    
    addResult('🚀 Starting Supabase integration tests...');

    // Test 1: Supabase Connection
    try {
      const { error } = await supabase.from('users').select('id').limit(1);
      if (error) {
        addResult('❌ Supabase connection failed: ' + error.message);
      } else {
        addResult('✅ Supabase connection successful');
      }
    } catch (error) {
      addResult('❌ Supabase connection error: ' + String(error));
    }

    // Test 2: Favorites Storage
    try {
      addResult('🧪 Testing favorites storage...');
      
      // Add a favorite
      await favoritesStorage.addFavorite('test-cocktail-1');
      addResult('✅ Added favorite');
      
      // Get favorites
      const favorites = await favoritesStorage.getFavorites();
      if (favorites.includes('test-cocktail-1')) {
        addResult('✅ Retrieved favorites successfully');
      } else {
        addResult('❌ Failed to retrieve favorites');
      }
      
      // Remove favorite
      await favoritesStorage.removeFavorite('test-cocktail-1');
      const updatedFavorites = await favoritesStorage.getFavorites();
      if (!updatedFavorites.includes('test-cocktail-1')) {
        addResult('✅ Removed favorite successfully');
      } else {
        addResult('❌ Failed to remove favorite');
      }
    } catch (error) {
      addResult('❌ Favorites storage error: ' + String(error));
    }

    // Test 3: Shopping List Storage
    try {
      addResult('🧪 Testing shopping list storage...');
      
      const testItem = {
        ingredient: {
          id: 'test-ingredient',
          name: 'Test Ingredient',
          category: IngredientCategory.OTHER,
          alcoholic: false
        },
        amount: '1 oz',
        cocktails: ['Test Cocktail']
      };
      
      // Save shopping list
      await shoppingListStorage.saveShoppingList([testItem]);
      addResult('✅ Saved shopping list');
      
      // Get shopping list
      const shoppingList = await shoppingListStorage.getShoppingList();
      if (shoppingList.length > 0) {
        addResult('✅ Retrieved shopping list successfully');
      } else {
        addResult('❌ Failed to retrieve shopping list');
      }
      
      // Clear shopping list
      await shoppingListStorage.saveShoppingList([]);
      const clearedList = await shoppingListStorage.getShoppingList();
      if (clearedList.length === 0) {
        addResult('✅ Cleared shopping list successfully');
      } else {
        addResult('❌ Failed to clear shopping list');
      }
    } catch (error) {
      addResult('❌ Shopping list storage error: ' + String(error));
    }

    // Test 4: Admin Data Storage
    try {
      addResult('🧪 Testing admin data storage...');
      
      // Get cocktails
      const cocktails = await adminDataStorage.getCocktails();
      addResult(`✅ Retrieved ${cocktails.length} cocktails`);
      
      // Get ingredients
      const ingredients = await adminDataStorage.getIngredients();
      addResult(`✅ Retrieved ${ingredients.length} ingredients`);
      
      // Get glass types
      const glassTypes = await adminDataStorage.getGlassTypes();
      addResult(`✅ Retrieved ${glassTypes.length} glass types`);
    } catch (error) {
      addResult('❌ Admin data storage error: ' + String(error));
    }

    // Test 5: LocalStorage Fallback
    try {
      addResult('🧪 Testing localStorage fallback...');
      
      // Test by temporarily disabling network (simulation)
      const originalFetch = window.fetch;
      window.fetch = () => Promise.reject(new Error('Network disabled for test'));
      
      // Try to add favorite (should fall back to localStorage)
      await favoritesStorage.addFavorite('test-fallback-cocktail');
      
      // Check localStorage directly
      const localStorageFavorites = JSON.parse(localStorage.getItem('cocktailflow-favorites') || '[]');
      if (localStorageFavorites.includes('test-fallback-cocktail')) {
        addResult('✅ LocalStorage fallback working');
      } else {
        addResult('❌ LocalStorage fallback failed');
      }
      
      // Clean up
      await favoritesStorage.removeFavorite('test-fallback-cocktail');
      window.fetch = originalFetch;
    } catch (error) {
      addResult('❌ LocalStorage fallback error: ' + String(error));
    }

    addResult('🏁 Tests completed!');
    setIsRunning(false);
  };

  return (
    <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <div className="text-center mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-4">Supabase Integration Test</h1>
        <p className="text-xl text-gray-600">
          Test the Supabase integration with localStorage fallback
        </p>
      </div>

      <div className="bg-white rounded-lg shadow-md p-6 mb-8">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-lg font-semibold text-gray-900">Test Results</h2>
          <button
            onClick={runTests}
            disabled={isRunning}
            className={`px-4 py-2 rounded-lg font-medium ${
              isRunning
                ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                : 'bg-blue-600 text-white hover:bg-blue-700'
            }`}
          >
            {isRunning ? 'Running Tests...' : 'Run Tests'}
          </button>
        </div>

        <div className="bg-gray-50 rounded-lg p-4 min-h-[400px]">
          {testResults.length === 0 ? (
            <p className="text-gray-500 text-center py-8">
              Click &quot;Run Tests&quot; to start testing the integration
            </p>
          ) : (
            <div className="space-y-2">
              {testResults.map((result, index) => (
                <div
                  key={index}
                  className={`text-sm font-mono p-2 rounded ${
                    result.includes('✅')
                      ? 'bg-green-100 text-green-800'
                      : result.includes('❌')
                      ? 'bg-red-100 text-red-800'
                      : result.includes('🧪')
                      ? 'bg-blue-100 text-blue-800'
                      : 'bg-gray-100 text-gray-800'
                  }`}
                >
                  {result}
                </div>
              ))}
            </div>
          )}
        </div>
      </div>

      <div className="bg-blue-50 rounded-lg p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">What This Tests</h3>
        <ul className="space-y-2 text-gray-700">
          <li>• Supabase database connection</li>
          <li>• Favorites storage and retrieval</li>
          <li>• Shopping list management</li>
          <li>• Admin data access (cocktails, ingredients, glass types)</li>
          <li>• LocalStorage fallback when Supabase is unavailable</li>
        </ul>
      </div>
    </div>
  );
}
