@import "tailwindcss";

:root {
  --background: #ededed;
  --foreground: #171717;
}

/* @theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
} */

@media (prefers-color-scheme: dark) {
  :root {
    --background: #ededed;
    --foreground: #171717;
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: Arial, Helvetica, sans-serif;
}

/* Custom animations and styles for Tipple */

/* Utility classes */
.line-clamp-1 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
  line-clamp: 1;
}

.line-clamp-2 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  line-clamp: 2;
}

/* Smooth hover animations */
.cocktail-card {
  transition: all 0.3s ease-in-out;
}

.cocktail-card:hover {
  transform: scale(1.05);
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
}

/* Glass shimmer effect */
.glass-shimmer {
  position: relative;
  overflow: hidden;
}

.glass-shimmer::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.3),
    transparent
  );
  transition: left 0.5s ease-in-out;
}

.glass-shimmer:hover::before {
  left: 100%;
}

/* Floating animation */
.float-animation {
  animation: float 3s ease-in-out infinite;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

/* Pulse animation for favorites */
.pulse-heart {
  animation: pulse-heart 0.3s ease-in-out;
}

@keyframes pulse-heart {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.3);
  }
  100% {
    transform: scale(1);
  }
}

/* Gradient backgrounds */
.gradient-cocktail {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.gradient-tropical {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.gradient-classic {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.gradient-modern {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

/* Custom scrollbar */
.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
