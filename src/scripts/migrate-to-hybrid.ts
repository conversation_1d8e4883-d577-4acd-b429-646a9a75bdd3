/**
 * Migration script to transition from old localStorage-based storage to new hybrid architecture
 * This script should be run once to migrate existing user data
 */

import { hybridStorage } from '@/lib/hybridStorage';
import { indexedDBStorage } from '@/lib/indexedDB';
import { cocktails } from '@/data/cocktails';
import { ingredients } from '@/data/ingredients';
import { glassTypes } from '@/data/glassTypes';

// Legacy storage keys
const LEGACY_KEYS = {
  FAVORITES: 'cocktailflow-favorites',
  SHOPPING_LIST: 'cocktailflow-shopping-list',
  COCKTAILS: 'cocktailflow-admin-cocktails',
  INGREDIENTS: 'cocktailflow-admin-ingredients',
  GLASS_TYPES: 'cocktailflow-admin-glass-types',
  USER_SESSION: 'cocktailflow-user-session',
} as const;

interface MigrationResult {
  success: boolean;
  message: string;
  data?: any;
}

/**
 * Migrate legacy localStorage data to hybrid storage
 */
export async function migrateLegacyData(): Promise<MigrationResult[]> {
  const results: MigrationResult[] = [];
  
  try {
    // Initialize hybrid storage
    await hybridStorage.init();
    results.push({ success: true, message: 'Hybrid storage initialized' });

    // Migrate default data first
    await migrateDefaultData(results);
    
    // Migrate user data
    await migrateFavorites(results);
    await migrateShoppingList(results);
    await migrateUserSession(results);
    
    // Mark migration as complete
    localStorage.setItem('tipple-migration-complete', new Date().toISOString());
    results.push({ success: true, message: 'Migration completed successfully' });
    
  } catch (error) {
    results.push({ 
      success: false, 
      message: `Migration failed: ${error instanceof Error ? error.message : 'Unknown error'}` 
    });
  }
  
  return results;
}

/**
 * Migrate default cocktail data
 */
async function migrateDefaultData(results: MigrationResult[]): Promise<void> {
  try {
    // Check if we already have data
    const existingCocktails = await hybridStorage.getCocktails();
    
    if (existingCocktails.length === 0) {
      // Migrate default cocktails
      for (const cocktail of cocktails) {
        await hybridStorage.saveCocktail(cocktail);
      }
      results.push({ 
        success: true, 
        message: `Migrated ${cocktails.length} default cocktails` 
      });
    } else {
      results.push({ 
        success: true, 
        message: `Found ${existingCocktails.length} existing cocktails, skipping default migration` 
      });
    }

    // Check ingredients
    const existingIngredients = await hybridStorage.getIngredients();
    
    if (existingIngredients.length === 0) {
      // Migrate default ingredients
      for (const ingredient of ingredients) {
        await hybridStorage.saveIngredient(ingredient);
      }
      results.push({ 
        success: true, 
        message: `Migrated ${ingredients.length} default ingredients` 
      });
    } else {
      results.push({ 
        success: true, 
        message: `Found ${existingIngredients.length} existing ingredients, skipping default migration` 
      });
    }

    // Check glass types
    const existingGlassTypes = await hybridStorage.getGlassTypes();
    
    if (existingGlassTypes.length === 0) {
      // Migrate default glass types
      for (const glassType of glassTypes) {
        await hybridStorage.saveGlassType(glassType);
      }
      results.push({ 
        success: true, 
        message: `Migrated ${glassTypes.length} default glass types` 
      });
    } else {
      results.push({ 
        success: true, 
        message: `Found ${existingGlassTypes.length} existing glass types, skipping default migration` 
      });
    }

  } catch (error) {
    results.push({ 
      success: false, 
      message: `Default data migration failed: ${error}` 
    });
  }
}

/**
 * Migrate user favorites
 */
async function migrateFavorites(results: MigrationResult[]): Promise<void> {
  try {
    const legacyFavorites = localStorage.getItem(LEGACY_KEYS.FAVORITES);
    
    if (legacyFavorites) {
      const favorites: string[] = JSON.parse(legacyFavorites);
      
      for (const cocktailId of favorites) {
        await hybridStorage.addFavorite(cocktailId);
      }
      
      results.push({ 
        success: true, 
        message: `Migrated ${favorites.length} favorites`,
        data: favorites 
      });
      
      // Keep legacy data for now, will be cleaned up later
    } else {
      results.push({ 
        success: true, 
        message: 'No legacy favorites found' 
      });
    }
    
  } catch (error) {
    results.push({ 
      success: false, 
      message: `Favorites migration failed: ${error}` 
    });
  }
}

/**
 * Migrate shopping list
 */
async function migrateShoppingList(results: MigrationResult[]): Promise<void> {
  try {
    const legacyShoppingList = localStorage.getItem(LEGACY_KEYS.SHOPPING_LIST);
    
    if (legacyShoppingList) {
      const shoppingList = JSON.parse(legacyShoppingList);
      
      await hybridStorage.saveShoppingList(shoppingList);
      
      results.push({ 
        success: true, 
        message: `Migrated ${shoppingList.length} shopping list items`,
        data: shoppingList 
      });
      
    } else {
      results.push({ 
        success: true, 
        message: 'No legacy shopping list found' 
      });
    }
    
  } catch (error) {
    results.push({ 
      success: false, 
      message: `Shopping list migration failed: ${error}` 
    });
  }
}

/**
 * Migrate user session
 */
async function migrateUserSession(results: MigrationResult[]): Promise<void> {
  try {
    const legacySession = localStorage.getItem(LEGACY_KEYS.USER_SESSION);
    
    if (legacySession) {
      const session = JSON.parse(legacySession);
      
      // Store anonymous user ID in IndexedDB
      await indexedDBStorage.setMetadata('anonymousUserId', session.id);
      await indexedDBStorage.setMetadata('anonymousUserCreatedAt', session.createdAt);
      
      results.push({ 
        success: true, 
        message: 'Migrated user session',
        data: { id: session.id, createdAt: session.createdAt } 
      });
      
    } else {
      results.push({ 
        success: true, 
        message: 'No legacy user session found' 
      });
    }
    
  } catch (error) {
    results.push({ 
      success: false, 
      message: `User session migration failed: ${error}` 
    });
  }
}

/**
 * Clean up legacy localStorage data (call this after successful migration)
 */
export function cleanupLegacyData(): MigrationResult[] {
  const results: MigrationResult[] = [];
  
  try {
    Object.values(LEGACY_KEYS).forEach(key => {
      if (localStorage.getItem(key)) {
        localStorage.removeItem(key);
        results.push({ 
          success: true, 
          message: `Cleaned up legacy key: ${key}` 
        });
      }
    });
    
    results.push({ 
      success: true, 
      message: 'Legacy data cleanup completed' 
    });
    
  } catch (error) {
    results.push({ 
      success: false, 
      message: `Cleanup failed: ${error}` 
    });
  }
  
  return results;
}

/**
 * Check if migration is needed
 */
export function isMigrationNeeded(): boolean {
  // Check if migration has already been completed
  const migrationComplete = localStorage.getItem('tipple-migration-complete');
  if (migrationComplete) {
    return false;
  }
  
  // Check if there's any legacy data to migrate
  const hasLegacyData = Object.values(LEGACY_KEYS).some(key => 
    localStorage.getItem(key) !== null
  );
  
  return hasLegacyData;
}

/**
 * Get migration status
 */
export function getMigrationStatus(): {
  isComplete: boolean;
  completedAt?: string;
  hasLegacyData: boolean;
  legacyDataKeys: string[];
} {
  const migrationComplete = localStorage.getItem('tipple-migration-complete');
  const legacyDataKeys = Object.values(LEGACY_KEYS).filter(key => 
    localStorage.getItem(key) !== null
  );
  
  return {
    isComplete: !!migrationComplete,
    completedAt: migrationComplete || undefined,
    hasLegacyData: legacyDataKeys.length > 0,
    legacyDataKeys
  };
}

/**
 * Auto-run migration if needed (call this on app startup)
 */
export async function autoMigrate(): Promise<void> {
  if (typeof window === 'undefined') return;
  
  if (isMigrationNeeded()) {
    console.log('Legacy data detected, starting migration...');
    
    try {
      const results = await migrateLegacyData();
      const failed = results.filter(r => !r.success);
      
      if (failed.length === 0) {
        console.log('Migration completed successfully');
        console.table(results);
      } else {
        console.warn('Migration completed with errors');
        console.table(results);
      }
      
    } catch (error) {
      console.error('Migration failed:', error);
    }
  }
}

// Auto-run migration on module load
if (typeof window !== 'undefined') {
  // Delay migration to avoid blocking app startup
  setTimeout(autoMigrate, 1000);
}
