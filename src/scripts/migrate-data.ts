/**
 * Data migration script to populate Supabase with initial data
 * Run this script to migrate your existing data to Supabase
 */

import { supabase } from '../lib/supabase';
import { cocktails } from '../data/cocktails';
import { ingredients, glassTypes } from '../data/ingredients';

async function migrateData() {
  console.log('Starting data migration to Supabase...');

  try {
    // Migrate ingredients first (cocktails depend on them)
    console.log('Migrating ingredients...');
    const { error: ingredientsError } = await supabase
      .from('ingredients')
      .upsert(ingredients.map(ingredient => ({
        id: ingredient.id,
        name: ingredient.name,
        category: ingredient.category,
        description: ingredient.description,
        abv: ingredient.abv
      })));

    if (ingredientsError) {
      console.error('Error migrating ingredients:', ingredientsError);
      return;
    }
    console.log(`✅ Migrated ${ingredients.length} ingredients`);

    // Migrate glass types
    console.log('Migrating glass types...');
    const { error: glassTypesError } = await supabase
      .from('glass_types')
      .upsert(glassTypes.map(glassType => ({
        id: glassType.id,
        name: glassType.name,
        description: glassType.description
      })));

    if (glassTypesError) {
      console.error('Error migrating glass types:', glassTypesError);
      return;
    }
    console.log(`✅ Migrated ${glassTypes.length} glass types`);

    // Migrate cocktails
    console.log('Migrating cocktails...');
    const { error: cocktailsError } = await supabase
      .from('cocktails')
      .upsert(cocktails.map(cocktail => ({
        id: cocktail.id,
        name: cocktail.name,
        description: cocktail.description,
        instructions: cocktail.instructions,
        ingredients: cocktail.ingredients,
        category: cocktail.category,
        difficulty: cocktail.difficulty,
        prep_time: cocktail.prepTime,
        glass_type: cocktail.glassType,
        garnish: cocktail.garnish,
        tags: cocktail.tags,
        image_url: cocktail.imageUrl
      })));

    if (cocktailsError) {
      console.error('Error migrating cocktails:', cocktailsError);
      return;
    }
    console.log(`✅ Migrated ${cocktails.length} cocktails`);

    console.log('🎉 Data migration completed successfully!');
  } catch (error) {
    console.error('Migration failed:', error);
  }
}

// Run migration if this file is executed directly
if (require.main === module) {
  migrateData();
}

export { migrateData };
