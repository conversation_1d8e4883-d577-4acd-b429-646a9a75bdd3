'use client';

import { useEffect } from 'react';

/**
 * Migration Handler Component
 * Automatically handles migration from legacy localStorage to hybrid storage
 * This component runs silently in the background
 */
export default function MigrationHandler() {
  useEffect(() => {
    // Dynamic import to avoid SSR issues and reduce bundle size
    const runMigration = async () => {
      try {
        const { autoMigrate } = await import('@/scripts/migrate-to-hybrid');
        await autoMigrate();
      } catch (error) {
        console.warn('Migration script failed to load or execute:', error);
      }
    };

    // Run migration after a short delay to avoid blocking initial render
    const timer = setTimeout(runMigration, 2000);
    
    return () => clearTimeout(timer);
  }, []);

  // This component renders nothing - it just handles migration in the background
  return null;
}
