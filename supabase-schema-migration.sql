-- Cocktail Flow Database Schema Migration
-- Run these commands in your Supabase SQL editor to update existing schema

-- First, let's add the missing columns to existing tables

-- Update ingredients table to add alcoholic field
ALTER TABLE public.ingredients 
ADD COLUMN IF NOT EXISTS alcoholic BOOLEAN DEFAULT FALSE;

-- Update the alcoholic field based on ABV (if ABV > 0, it's alcoholic)
UPDATE public.ingredients 
SET alcoholic = CASE 
    WHEN abv IS NOT NULL AND abv > 0 THEN TRUE 
    ELSE FALSE 
END
WHERE alcoholic IS NULL;

-- Make alcoholic field NOT NULL after setting values
ALTER TABLE public.ingredients 
ALTER COLUMN alcoholic SET NOT NULL;

-- Update glass_types table to add missing fields
ALTER TABLE public.glass_types 
ADD COLUMN IF NOT EXISTS icon_url TEXT,
ADD COLUMN IF NOT EXISTS capacity TEXT;

-- Make description NOT NULL (set default for existing records)
UPDATE public.glass_types 
SET description = COALESCE(description, 'Classic cocktail glass')
WHERE description IS NULL;

ALTER TABLE public.glass_types 
ALTER COLUMN description SET NOT NULL;

-- Update cocktails table structure
ALTER TABLE public.cocktails 
ADD COLUMN IF NOT EXISTS glass_type_data JSONB,
ADD COLUMN IF NOT EXISTS servings INTEGER DEFAULT 1,
ADD COLUMN IF NOT EXISTS history TEXT,
ADD COLUMN IF NOT EXISTS variations TEXT[];

-- Make servings NOT NULL after adding default
ALTER TABLE public.cocktails 
ALTER COLUMN servings SET NOT NULL;

-- Make garnish nullable (it was NOT NULL before)
ALTER TABLE public.cocktails 
ALTER COLUMN garnish DROP NOT NULL;

-- Create a function to migrate existing cocktail data
CREATE OR REPLACE FUNCTION migrate_cocktail_data()
RETURNS void AS $$
DECLARE
    cocktail_record RECORD;
    glass_data JSONB;
BEGIN
    -- Loop through all cocktails and update glass_type_data
    FOR cocktail_record IN 
        SELECT id, glass_type FROM public.cocktails 
        WHERE glass_type_data IS NULL
    LOOP
        -- Get the glass type data
        SELECT to_jsonb(gt.*) INTO glass_data
        FROM public.glass_types gt
        WHERE gt.id = cocktail_record.glass_type;
        
        -- If glass type found, update the cocktail
        IF glass_data IS NOT NULL THEN
            UPDATE public.cocktails 
            SET glass_type_data = glass_data
            WHERE id = cocktail_record.id;
        ELSE
            -- Create a default glass type if not found
            UPDATE public.cocktails 
            SET glass_type_data = jsonb_build_object(
                'id', cocktail_record.glass_type,
                'name', initcap(replace(cocktail_record.glass_type, '-', ' ')),
                'description', 'Classic cocktail glass',
                'capacity', '8-10 oz'
            )
            WHERE id = cocktail_record.id;
        END IF;
    END LOOP;
END;
$$ LANGUAGE plpgsql;

-- Run the migration function
SELECT migrate_cocktail_data();

-- Drop the migration function as it's no longer needed
DROP FUNCTION migrate_cocktail_data();

-- Now make glass_type_data NOT NULL
ALTER TABLE public.cocktails 
ALTER COLUMN glass_type_data SET NOT NULL;

-- Create indexes for the new JSONB columns
CREATE INDEX IF NOT EXISTS idx_cocktails_glass_type_data ON public.cocktails USING GIN (glass_type_data);
CREATE INDEX IF NOT EXISTS idx_cocktails_ingredients ON public.cocktails USING GIN (ingredients);

-- Add check constraints to ensure data integrity
ALTER TABLE public.cocktails 
ADD CONSTRAINT check_glass_type_data_structure 
CHECK (
    glass_type_data ? 'id' AND 
    glass_type_data ? 'name' AND 
    glass_type_data ? 'description'
);

ALTER TABLE public.cocktails 
ADD CONSTRAINT check_ingredients_is_array 
CHECK (jsonb_typeof(ingredients) = 'array');

-- Update the existing triggers to include new columns
DROP TRIGGER IF EXISTS update_cocktails_updated_at ON public.cocktails;
CREATE TRIGGER update_cocktails_updated_at BEFORE UPDATE ON public.cocktails
    FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();

DROP TRIGGER IF EXISTS update_ingredients_updated_at ON public.ingredients;
CREATE TRIGGER update_ingredients_updated_at BEFORE UPDATE ON public.ingredients
    FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();

DROP TRIGGER IF EXISTS update_glass_types_updated_at ON public.glass_types;
CREATE TRIGGER update_glass_types_updated_at BEFORE UPDATE ON public.glass_types
    FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();

-- Create a view for easier cocktail querying with proper typing
CREATE OR REPLACE VIEW public.cocktails_with_types AS
SELECT 
    c.id,
    c.name,
    c.description,
    c.instructions,
    c.ingredients,
    c.glass_type_data,
    c.category,
    c.difficulty,
    c.prep_time,
    c.servings,
    c.garnish,
    c.tags,
    c.image_url,
    c.history,
    c.variations,
    c.created_at,
    c.updated_at
FROM public.cocktails c;

-- Grant permissions on the view
GRANT SELECT ON public.cocktails_with_types TO authenticated, anon;

-- Create helper functions for data validation
CREATE OR REPLACE FUNCTION validate_cocktail_ingredient(ingredient_data JSONB)
RETURNS BOOLEAN AS $$
BEGIN
    RETURN (
        ingredient_data ? 'ingredient' AND
        ingredient_data ? 'amount' AND
        (ingredient_data->'ingredient') ? 'id' AND
        (ingredient_data->'ingredient') ? 'name' AND
        (ingredient_data->'ingredient') ? 'category' AND
        (ingredient_data->'ingredient') ? 'alcoholic'
    );
END;
$$ LANGUAGE plpgsql IMMUTABLE;

-- Add constraint to validate ingredient structure
ALTER TABLE public.cocktails 
ADD CONSTRAINT check_ingredients_structure 
CHECK (
    CASE 
        WHEN jsonb_typeof(ingredients) = 'array' THEN
            (SELECT bool_and(validate_cocktail_ingredient(ingredient))
             FROM jsonb_array_elements(ingredients) AS ingredient)
        ELSE FALSE
    END
);
